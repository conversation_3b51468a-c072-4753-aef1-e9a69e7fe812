using Core.DataAccess;
using Core.Utilities.Results;
using Entities.Concrete;
using Entities.DTOs;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DataAccess.Abstract
{
    public interface ITransactionDal : IEntityRepository<Transaction>
    {
        List<TransactionDetailDto> GetTransactionsWithDetails();

        // SOLID prensiplerine uygun: Karmaşık iş mantığı DAL katmanına taşındı
        IResult AddBulkTransactionWithBusinessLogic(BulkTransactionDto bulkTransaction, int companyId);
        IResult AddTransactionWithBusinessLogic(Transaction transaction, int companyId);
        IResult UpdatePaymentStatusWithBusinessLogic(int transactionId, int companyId);
        IResult UpdateAllPaymentStatusWithBusinessLogic(int memberId, int companyId);

        // SOLID prensiplerine uygun: Validation ve soft delete logic DAL katmanında
        IResult SoftDeleteTransactionWithValidation(int transactionId, int companyId);

        // SOLID prensiplerine uygun: Filtreleme ve hesaplama işlemleri DAL katmanında
        List<TransactionDetailDto> GetUnpaidTransactionsByMemberId(int memberId);
        decimal GetMonthlyTransactionTotal(int year, int month);
        decimal GetDailyTransactionTotal(DateTime date);
    }
}
