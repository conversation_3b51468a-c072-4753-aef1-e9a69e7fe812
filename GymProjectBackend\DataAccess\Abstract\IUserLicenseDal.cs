﻿using Core.DataAccess;
using Core.Utilities.Results;
using Entities.Concrete;
using Entities.DTOs;

namespace DataAccess.Abstract
{
    public interface IUserLicenseDal : IEntityRepository<UserLicense>
    {
        List<UserLicenseDto> GetUserLicenseDetails();
        PaginatedUserLicenseDto GetUserLicenseDetailsPaginated(int page, int pageSize, string searchTerm, string sortBy, string companyName, int? remainingDaysMin, int? remainingDaysMax);
        PaginatedUserLicenseDto GetExpiredAndPassiveLicenses(int page, int pageSize, string searchTerm);
        List<UserLicenseDto> GetActiveUserLicensesByUserId(int userId);
        UserLicenseDto GetUserLicenseDetail(int userLicenseId);
        IResult PurchaseLicense(LicensePurchaseDto licensePurchaseDto);
        IResult ExtendLicenseByPackage(LicenseExtensionByPackageDto licenseExtensionByPackageDto);
        List<string> GetUserRoles(int userId);

        // SOLID prensiplerine uygun: Validation ve entity manipulation logic DAL katmanında
        IResult RevokeLicenseWithValidation(int userLicenseId);
    }

}
